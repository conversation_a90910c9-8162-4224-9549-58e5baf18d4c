import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  Divider,
  SvgIcon,
  TableHead,
  TableBody,
  TableCell,
  TableRow,
  Typography,
  Box,
  Chip
} from "@mui/material";
import { centsToPrice, priceToCents } from "itsf-ui-common";
import { useViewAdjustments } from "./useViewAdjusments";
import { IElligibleInvoiceResponse } from "@modules/financial-document/interfaces/responses/ICreditNotesResponses";
import { IAdjustmentResponseDTO } from "@modules/adjustment/interfaces/payloads/IAdjustmentPayload";
import { useGetQueryParams } from "@hooks/useGetQueryParams";
import { useLocation } from "react-router-dom";
import { useSnackBar } from "@common/SnackBar";
import {
  SectionCard,
  SimpleTable,
  useOutlinedCardStyle,
  useSectionIconStyle,
  FormButton
} from "@common";
import { ReactComponent as BillIcon } from "@static/icons/bill.svg";
import OperationResultDialog from "@common/Dialog/AdjusmentsDialog/OperationResultDialog";
import { OperationConfirmDialog } from "@common/Dialog/AdjusmentsDialog/OperationConfirmDialog";
import { AdjustmentApprovalDialog } from "@common/Dialog/AdjusmentsDialog/AdjustmentApprovalDialog";
import { useTranslation } from "react-i18next";



interface IPropsViewAdjusments {
  accountId: string;
}

const ViewAdjusments = ({ accountId }: IPropsViewAdjusments) => {
  const { caseid: caseIdFromQuery } = useGetQueryParams("caseid");
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const caseIdFromLocation = searchParams.get("caseid");
  const { setSnackBarError } = useSnackBar();
  const { t } = useTranslation(["customer"]);

  const { classes: outlinedCardClasses } = useOutlinedCardStyle();
  const { classes: iconClasses } = useSectionIconStyle();

  const statusColors: Record<string, "default" | "primary" | "success" | "error" | "warning" | "info"> = {
    init: "default",
    approved: "primary",
    denied: "error",
    dispute: "warning",
    completed: "success",
  };

  const {
    adjustmentsList,
    isLoadingAdjustments,
    loadAdjustmentsFromEligibleInvoices,
    eligibleInvoices,
    setCaseId,
    caseId,
    createCreditNoteFromAdjustment,
    updateAdjustmentStatus,
    caseVerified,
    setCaseVerified,
    canApprove,
    maxAcceptableAmountAutoApprovalCreditNoteCents,
    callAdjusmentCustomServiceGetAdjusment,
    verifyCaseById,
  } = useViewAdjustments({ accountId });

  const [showEligibleInvoices, setShowEligibleInvoices] = useState<boolean>(false);
  const [selectedInvoice, setSelectedInvoice] = useState<IElligibleInvoiceResponse | null>(null);
  const [creditNoteAmounts, setCreditNoteAmounts] = useState<{ [key: string]: number }>({});
  const [selectedAdjustment, setSelectedAdjustment] = useState<IAdjustmentResponseDTO | null>(null);
  const [isAuthorizingCreditNote, setIsAuthorizingCreditNote] = useState(false);
  const [isCreatingCreditNote, setIsCreatingCreditNote] = useState(false);
  const [isUpdatingStatusCreditNote, setIsUpdatingStatusCreditNote] = useState(false);

  //Variables para manejar la visualizacion del los dialogos
  const [isShowOperationResultDialog, setIsShowOperationResultDialog] = useState(false);
  const [isShowOperationConfirmDialog, setIsShowOperationConfirmDialog] = useState(false);
  const [isShowAdjustmentApprovalDialog, setIsShowAdjustmentApprovalDialog] = useState(false);
  const [isShowAdjustmentStatusChangeDialog, setIsShowAdjustmentStatusChangeDialog] = useState(false);
  const [isShowAdjustmentApprovalDialogPostCreation, setIsShowAdjustmentApprovalDialogPostCreation] = useState(false);
  
  const [descriptionChangeStatus, setDescriptionChangeStatus] = useState(String);
  const [currentIdAdjustment, setCurrentIdAdjustment] = useState<number>(0);

  const isCreateMode = showEligibleInvoices && !selectedAdjustment;

  useEffect(() => {
    const caseIdValue = caseIdFromLocation || caseIdFromQuery;
    const caseIdToUse = caseIdValue ? parseInt(caseIdValue, 10) : 0;
    setCaseId(caseIdToUse);
  }, [setCaseId, caseIdFromQuery, caseIdFromLocation]);

  const initAmountsFromInvoice = (invoice: IElligibleInvoiceResponse) => {
    const initialAmounts: { [key: string]: number } = {};
    invoice.items?.forEach((item) => {
      if (item.billing_section_id) initialAmounts[item.billing_section_id] = 0;
    });
    setCreditNoteAmounts(initialAmounts);
  };

  const handleCreateCreditNoteClick = () => {
    setSelectedAdjustment(null);
    setShowEligibleInvoices(true);
  };

  const handleInvoiceSelect = (invoice: IElligibleInvoiceResponse) => {
    setSelectedInvoice(invoice);
    initAmountsFromInvoice(invoice);
  };

  const handleInvoiceChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const value = e.target.value;
    const invoice = eligibleInvoices.find(inv => inv.invoice_number === value);
    setSelectedAdjustment(null);
    if (invoice) {
      handleInvoiceSelect(invoice);
    } else {
      setSelectedInvoice(null);
      setCreditNoteAmounts({});
    }
  };

  const handleAmountChange = (billingSeccionId: string, value: number, maxAmountInCents: number) => {
    const maxAmount = parseFloat(centsToPrice(maxAmountInCents));
    if (value >= 0 && value <= maxAmount) {
      setCreditNoteAmounts(prev => ({ ...prev, [billingSeccionId]: value }));
    }
  };

  const handleCreateCreditNote = async (): Promise<number> => {
    if (!selectedInvoice || !selectedInvoice.invoice_number) {
      return 0;
    }

    const details = Object.entries(creditNoteAmounts)
      .filter(([_, amount]) => amount > 0)
      .map(([billingSeccionId, amount]) => ({
        idRubro: billingSeccionId,
        amount: priceToCents(amount.toString()),
      }));

    if (details.length === 0) {
      setSnackBarError("Debe ingresar al menos un monto mayor a 0");
      return 0;
    }

    try {
      const success = await createCreditNoteFromAdjustment({
        invoiceId: parseInt(selectedInvoice.invoice_number, 10),
        details,
      });

      return success; 
    } catch (error) {
      console.error("Error creando Credit Note:", error);
      return 0;
    }
  };


  const handlePressCreateCreditNoteButtonTable = () => {
    if (!selectedInvoice || !selectedInvoice.invoice_number) return;

    const details = Object.entries(creditNoteAmounts)
      .filter(([_, amount]) => amount > 0)
      .map(([billingSeccionId, amount]) => ({
        idRubro: parseInt(billingSeccionId, 10),
        amount: priceToCents(amount.toString())
      }));

    if (details.length === 0) {
      setSnackBarError("Debe ingresar al menos un monto mayor a 0");
      return;
    }

    setIsShowOperationConfirmDialog(true);
  };

  const handleBackToList = () => {
    setSelectedInvoice(null);
    setSelectedAdjustment(null);
    setShowEligibleInvoices(false);
    setCreditNoteAmounts({});
  };

  const handleAuthorizeCreditNote = async (status: string) => {
    setIsUpdatingStatusCreditNote(true);
    if (!selectedAdjustment || !selectedAdjustment.id) {
      setSnackBarError("No hay adjustment seleccionado para autorizar");
      return;
    }

    setIsAuthorizingCreditNote(true);

    try {
      const success = await updateAdjustmentStatus({
        adjustmentId: selectedAdjustment.id,
        status: status as "approved" | "denied",
        adjustment: selectedAdjustment,
      });

      if (success) {
        setSelectedAdjustment(null);
        setShowEligibleInvoices(false);
        setSelectedInvoice(null);
        setCreditNoteAmounts({});
      }
    } catch (error) {
      console.error("Error autorizando credit note:", error);
    } finally {
      setIsAuthorizingCreditNote(false);
      setIsUpdatingStatusCreditNote(false);
      setIsShowAdjustmentApprovalDialogPostCreation(false)
    }
  };

  const handleViewDetailsFromAdjustment = (adj: IAdjustmentResponseDTO) => {
    const invoice = eligibleInvoices.find(inv => {
      const invNum = inv.invoice_number ? parseInt(inv.invoice_number, 10) : NaN;
      return Number.isFinite(invNum) && invNum === adj.invoiceId;
    });

    if (!invoice) {
      setSnackBarError(
        t("customer:invoiceDataNotFound")
      );
      return;
    }

    setShowEligibleInvoices(true);
    setCaseId(adj.caseId ?? 0);
    setSelectedAdjustment(adj);
    setSelectedInvoice(invoice);

    const initial: { [key: string]: number } = {};
    invoice.items?.forEach((item) => {
      if (item.billing_section_id) initial[item.billing_section_id] = 0;
    });

    adj.detail?.forEach(d => {
      initial[String(d.adjustmentSerialNo)] = parseFloat(centsToPrice(d.amount ?? 0));
    });

    setCreditNoteAmounts(initial);
  };

  const showAuthorizeButton =
    !!selectedAdjustment &&
    !["approved","denied" ,"completed"].includes(
      (selectedAdjustment.detail?.[0]?.status || "").toLowerCase()
    );


  //Este el metodo que se ejecuta cuando se presiona el boton aceptar en el dialogo de confirmacion de creacion de la request de la credit note
  const handlePressConfirmCreateButton = async () => {

    //Pregunto si el caso esta verificado
    if (caseVerified) {
      setIsCreatingCreditNote(true);
      try {
        const idAdjustment = await handleCreateCreditNote();

        if (idAdjustment === 0) {
          setSnackBarError(t("customer:errorCreatingCreditNote"));
          return;
        }

        console.log("idAdjustment",idAdjustment);
        console.log("adjustmentsList",adjustmentsList);

        // Busco el adjustment recien creado en la lista de adjustments
        const currentAdjusment = adjustmentsList.find(adjustment => adjustment.id === idAdjustment);

        //Guardo al id del adjustment recien creado para cambiarle el status si se tiene privilegios para aprobar o no
        setCurrentIdAdjustment(idAdjustment);


        console.log("currentAdjusment",currentAdjusment);
        console.log("currentAdjusment.totalAmount",currentAdjusment?.totalAmount);

        // Si no se encuentra el adjustment en la lista, intentar buscarlo directamente
        let finalAdjustment = currentAdjusment;
        if(!currentAdjusment) {
          console.log("Adjustment not found in list, trying direct search...");
          try {
            const directSearchResult = await callAdjusmentCustomServiceGetAdjusment({
              adjustmentId: idAdjustment
            });
            if (directSearchResult) {
              finalAdjustment = directSearchResult;
              console.log("Found adjustment via direct search:", finalAdjustment);
            } else {
              setSnackBarError("Error finding created adjustment");
              setIsShowOperationConfirmDialog(false);
              return;
            }
          } catch (error) {
            console.error("Error in direct search:", error);
            setSnackBarError(t("customer:errorFindingCreatedAdjustment"));
            setIsShowOperationConfirmDialog(false);
            return;
          }
        }

        if(!finalAdjustment || !finalAdjustment.totalAmount){
          setSnackBarError(t("customer:errorAdjustmentMissingAmount"));
          setIsShowOperationConfirmDialog(false);
          return;
        }



        //Las siguientes condiciones son para cambiar el status de la request de la credit note (adjusment)
        //Pregunto si el monto es mayor al maximo aceptable para auto-aprobar
        console.log("MAXIMO ACEPTABLE",maxAcceptableAmountAutoApprovalCreditNoteCents);
        console.log("MONTO",finalAdjustment.totalAmount);
        if(maxAcceptableAmountAutoApprovalCreditNoteCents < finalAdjustment.totalAmount){
          
          //Pregunto si el usuario tiene permisos para aprobar
          if(canApprove){
            //Mostrar dialogo para confirmar la accion de autorizar la nota de crédito
            setIsShowOperationConfirmDialog(false);
            setIsShowAdjustmentApprovalDialog(true);    
            console.log("SI PUEDE APROBAR");

          }else{
            //No tiene permisos para aprobar se muestre un snackbar indicando que no tiene permisos para aprobar
            
            //Hacer un patch al adjustment para colocarlo como dispute 
            const success = await updateAdjustmentStatus({
                adjustmentId: idAdjustment,
                status: "dispute",
                adjustment: finalAdjustment,
            });

            setIsShowOperationConfirmDialog(false);
            if(success){
              setSnackBarError(t("customer:noPermissionApprove"));
            }
            

          }

        }else{
          //El monto total de la credit note es menor al maximo aceptable, por lo tanto, se aprueba automaticamente
          // 1. Hacer un patch al adjustment para aprobarlo automaticamente
          
          
          const success = await updateAdjustmentStatus({
            adjustmentId: idAdjustment,
            status: "approved",
            adjustment: finalAdjustment,
          });

          // 2. Mostrar dialogo de final indicando que la operacion fue exitosa
          setIsShowOperationConfirmDialog(false);
          
          if(!success){
            setSnackBarError(t("customer:errorCreatingCreditNote"));
            return;
          }else{
              setIsShowOperationResultDialog(true);
          }

        }

        //Resetear las variables
        if (idAdjustment) {
          setSelectedInvoice(null);
          setCreditNoteAmounts({});
          setSelectedAdjustment(null);
          setShowEligibleInvoices(false);
        }
        setCaseVerified(false);
      } catch (error) {
        console.error("Error in handlePressConfirmCreateButton:", error);
        setSnackBarError(t("customer:errorCreatingCreditNote"));
      } finally {
        setIsCreatingCreditNote(false);
      }
    } else {
      setSnackBarError(t("customer:pleaseVerifyCase"));
    }

  };


  const handlePressApproveDeniedButton = async () => {
    if(canApprove){
      //Mostrar dialogo para confirmar la accion de autorizar la nota de crédito
      setIsShowAdjustmentApprovalDialogPostCreation(true);    
      console.log("SI PUEDE APROBAR");

    }else{      
      setSnackBarError(t("customer:noPermissionApprove"));
    }
  };






  const handlePressChangeStatusButton = async (status : string) => {
    let success = false;
    setIsUpdatingStatusCreditNote(true);
    try {
      const directSearchResult = await callAdjusmentCustomServiceGetAdjusment({
        adjustmentId: currentIdAdjustment
      });
      if (directSearchResult) {
        console.log("Found adjustment via direct search:", directSearchResult);
            switch (status) {
              case "approved":
                success = await updateAdjustmentStatus({
                  adjustmentId: currentIdAdjustment,
                  status: "approved",
                  adjustment: directSearchResult,
                });
                setDescriptionChangeStatus(t("customer:adjustmentApproved"));
                break;
              case "denied":
                success = await updateAdjustmentStatus({
                  adjustmentId: currentIdAdjustment,
                  status: "denied",
                  adjustment: directSearchResult,
                });
                setDescriptionChangeStatus(t("customer:adjustmentDenied"));
                break;
              case "dispute":
                  setIsShowAdjustmentApprovalDialog(false);
                  success = await updateAdjustmentStatus({
                    adjustmentId: currentIdAdjustment,
                    status: "dispute",
                    adjustment: directSearchResult,
                  });
                  break;
              default:
                setIsShowAdjustmentApprovalDialog(false);
                break;
            }
            setIsShowAdjustmentApprovalDialog(false);
                
            if(success){
              if(status === "approved" || status === "denied"){
                setIsShowAdjustmentStatusChangeDialog(true);
              }              
            }else{
              setSnackBarError(t("customer:errorChangingCreditNoteStatus"));
            }  
            setIsUpdatingStatusCreditNote(false);
      } else {
        setSnackBarError("Error finding created adjustment");
        setIsShowAdjustmentApprovalDialog(false);
        setIsUpdatingStatusCreditNote(false);
      }
    } catch (error) {
      console.error("Error in direct search:", error);
      setIsShowAdjustmentApprovalDialog(false);
      setIsUpdatingStatusCreditNote(false);
    }
  };

    

  return (
    <Box >
    <Card className={outlinedCardClasses.main} elevation={0} variant="outlined">
      <CardContent>
        <SectionCard
          icon={<SvgIcon className={iconClasses.main} component={BillIcon} viewBox="0 0 21 19" />}
          title={showEligibleInvoices ? t("customer:createCreditNote") : t("customer:adjustmentsList")}
          actionButtons={
            <Box display="flex" gap={1}>
              {!showEligibleInvoices && (
                <FormButton
                  buttonText={t("customer:createCreditNote")}
                  variant="contained"
                  color="primary"
                  onClick={handleCreateCreditNoteClick}
                />
              )}

              {!showEligibleInvoices && (
                <FormButton
                  buttonText={isLoadingAdjustments ? t("customer:loading") : t("customer:refresh")}
                  variant="outlined"
                  color="primary"
                  disabled={isLoadingAdjustments}
                  onClick={loadAdjustmentsFromEligibleInvoices}
                />
              )}

              {showEligibleInvoices && (
                <FormButton
                  buttonText={t("customer:back")}
                  variant="outlined"
                  color="secondary"
                  onClick={handleBackToList}
                />
              )}
            </Box>
          }
        >
          {showEligibleInvoices && !isLoadingAdjustments && (
            <Grid container direction="row" spacing={2} width="100%">
              <Grid item xs={12}>
                <Divider textAlign="center">{t("customer:caseNumber")}</Divider>
              </Grid>
              <Grid item xs={4}>
                <TextField
                  fullWidth
                  label={t("customer:caseNumber")}
                  type="number"
                  value={Number.isFinite(caseId) ? caseId : 0}
                  onChange={(e) => {
                    const v = parseInt(e.target.value, 10);
                    setCaseId(Number.isFinite(v) ? v : 0);
                  }}
                  disabled={!isCreateMode}
                />
              </Grid>
              <Grid item xs={2}>
                <Button
                  color="primary"
                  variant="outlined"
                  onClick={() => {
                    verifyCaseById(caseId);
                  }}
                  disabled={!isCreateMode}
                >
                  {t("customer:verify")}                  
                </Button>
              </Grid>
              <Grid item xs={12}>
                <Divider textAlign="center">{t("customer:invoiceDetails")}</Divider>
              </Grid>
              <Grid item xs={4}>
                <TextField
                  fullWidth
                  select
                  label={t("customer:invoice")}
                  value={selectedInvoice?.invoice_number ?? ""}
                  onChange={handleInvoiceChange}
                  SelectProps={{
                    native: true,
                  }}
                  disabled={!isCreateMode}
                >
                  <option value="" disabled>
                    {eligibleInvoices.length === 0
                      ? t("customer:noEligibleInvoices")
                      : ""}
                  </option>
                  {eligibleInvoices.map((inv, idx) => (
                    <option key={`${inv.invoice_number}-${idx}`} value={inv.invoice_number ?? ""}>
                      {inv.invoice_number} ({inv.items?.length ?? 0} {t("customer:items")})
                    </option>
                  ))}
                </TextField>
              </Grid>
            </Grid>
          )}

          {selectedInvoice && (
            <Box mt={2}>
              <SimpleTable cellTextColor="black">
                <TableHead>
                  <TableRow>
                    <TableCell>{t("customer:billingSectionId")}</TableCell>
                    <TableCell>{t("customer:description")}</TableCell>
                    <TableCell>{t("customer:availableAmount")}</TableCell>
                    <TableCell>{t("customer:creditNoteAmount")}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {selectedInvoice.items?.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>{item.billing_section_id}</TableCell>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>
                        ${centsToPrice(item.net_amount_left || 0)}
                      </TableCell>
                      <TableCell>
                        <TextField
                          type="number"
                          size="small"
                          inputProps={{
                            min: 0,
                            max: parseFloat(centsToPrice(item.net_amount_left || 0)),
                            step: 0.01
                          }}
                          value={creditNoteAmounts[item.billing_section_id || ""] || 0}
                          onChange={(e) => {
                            const value = parseFloat(e.target.value) || 0;
                            handleAmountChange(
                              item.billing_section_id || "",
                              value,
                              item.net_amount_left || 0
                            );
                          }}
                          sx={{ width: 150 }}
                          disabled={!isCreateMode}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </SimpleTable>

              <Box display="flex" justifyContent="flex-end" gap={2} mt={2}>
                {showAuthorizeButton && (
                  <FormButton
                    buttonText={isAuthorizingCreditNote ? t("customer:authorizing") : t("customer:authorizeCreditNote")}
                    variant="contained"
                    color="primary"
                    disabled={isAuthorizingCreditNote}
                    onClick={handlePressApproveDeniedButton}//handleAuthorizeCreditNote}
                  />
                )}
                {!selectedAdjustment && !showAuthorizeButton && (
                  <FormButton
                    buttonText={t("customer:createCreditNote")}
                    variant="contained"
                    color="primary"
                    onClick={handlePressCreateCreditNoteButtonTable}
                  />
                )}
              </Box>
            </Box>
          )}

          {!showEligibleInvoices && !isLoadingAdjustments && (
            <SimpleTable cellTextColor="black">
              <TableHead>
                <TableRow>
                  <TableCell>{t("customer:adjustment")}</TableCell>
                  <TableCell>{t("customer:invoice")}</TableCell>
                  <TableCell>{t("customer:case")}</TableCell>
                  <TableCell>{t("customer:totalAmount")}</TableCell>
                  <TableCell>{t("customer:type")}</TableCell>
                  <TableCell>{t("customer:status")}</TableCell>
                  <TableCell>{t("customer:creationDate")}</TableCell>
                  <TableCell>{t("customer:createdBy")}</TableCell>
                  <TableCell>{t("customer:detailsES")}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {adjustmentsList.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} align="center">
                      <Typography color="textSecondary">
                        {t("customer:noAdjustmentsFound")}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  adjustmentsList.map((adjustment) => (
                    <TableRow key={adjustment.id}>
                      <TableCell>{adjustment.id}</TableCell>
                      <TableCell>{adjustment.invoiceId}</TableCell>
                      <TableCell>{adjustment.caseId}</TableCell>
                      <TableCell>
                        ${centsToPrice(adjustment.totalAmount || 0)}
                      </TableCell>
                      <TableCell>{adjustment.type || ""}</TableCell>
                      <TableCell>
                          {adjustment.detail?.[0]?.status ? (
                            <Chip
                              label={adjustment.detail[0].status}
                              color={statusColors[adjustment.detail[0].status.toLowerCase()] || "default"}
                              size="small"
                            />
                          ) : (
                            ""
                          )}
                      </TableCell>
                      <TableCell>{adjustment.createdDate?.split("T")[0] || ""}</TableCell>
                      <TableCell>{adjustment.createdBy || "N/A"}</TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          variant="outlined"
                          color="primary"
                          onClick={() => handleViewDetailsFromAdjustment(adjustment)}
                        >
                          {t("customer:detailsES")}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </SimpleTable>
          )}
        </SectionCard>
      </CardContent>
    </Card>

      {/*DIALOGO PARA INDICAR QUE LA OPERACION DE LA REQUEST DE LA CREDIT NOTE FUE EXITOSA*/}
      <OperationResultDialog
        isOpen={isShowOperationResultDialog}
        onClose={() => setIsShowOperationResultDialog(false)} //REFRESCAR EL COMPONENTE
        message={t("customer:creditNoteCreatedSuccessfully")}
      />

      {/*DIALOGO PARA PREGUNTAR AL USUARIO SI DESEA CREAR LA REQUEST DE LA CREDIT NOTE*/}
      <OperationConfirmDialog
        isOpen={isShowOperationConfirmDialog}
        handleCancel={() => setIsShowOperationConfirmDialog(false)}
        handleConfirmOperations={handlePressConfirmCreateButton}
        title={t("customer:createCreditNote")}
        message={t("customer:areYouSureCreateCreditNote")}
        isLoading={isCreatingCreditNote}
        confirmButtonText={isCreatingCreditNote ? t("customer:creating") : t("customer:confirm")}
      />

      {/*DIALOGO PARA PREGUNTAR AL USUARIO SI DESEA APROBAR O DENEGAR LA REQUEST DE LA CREDIT NOTE (CUANDO SE CREA LA CREDIT NOTE)*/}
      <AdjustmentApprovalDialog
        isOpen={isShowAdjustmentApprovalDialog}
        handleCancel={() => handlePressChangeStatusButton("dispute")}
        handleClose={() => handlePressChangeStatusButton("dispute")}
        handleApproveOperations={()=> handlePressChangeStatusButton("approved")}
        handleDeniedOperations={()=> handlePressChangeStatusButton("denied")}
        title={t("customer:adjustmentApproval")}
        message={t("customer:areYouSureApproveAdjustment")}
        isLoading={isUpdatingStatusCreditNote}
        approveButtonText={t("customer:approve")}
        deniedButtonText={t("customer:deny")}
      /> 


      {/*DIALOGO PARA INDICAR QUE SE CAMBIO EL ESTATUS AL ADJUMENT A APPROVE O DENIED*/}
      <OperationResultDialog
        isOpen={isShowAdjustmentStatusChangeDialog}
        onClose={() => setIsShowAdjustmentStatusChangeDialog(false)} 
        message={descriptionChangeStatus}        
      />


      {/*DIALOGO PARA PREGUNTAR AL USUARIO SI DESEA APROBAR O DENEGAR LA REQUEST DE LA CREDIT NOTE (CUANDO SE AUTORIZA POSTERIOR A LA CREACION CUANDO EL USER ACTUAL NO TIENE PERMISOS)*/}
      <AdjustmentApprovalDialog
        isOpen={isShowAdjustmentApprovalDialogPostCreation}
        handleCancel={() => setIsShowAdjustmentApprovalDialogPostCreation(false)}
        handleClose={() => setIsShowAdjustmentApprovalDialogPostCreation(false)}
        handleApproveOperations={()=> handleAuthorizeCreditNote("approved")}
        handleDeniedOperations={()=> handleAuthorizeCreditNote("denied")}
        title={t("customer:adjustmentApproval")}
        message={t("customer:areYouSureApproveAdjustment")}
        isLoading={isUpdatingStatusCreditNote}
        approveButtonText={t("customer:approve")}
        deniedButtonText={t("customer:deny")}
      /> 



    </Box>


  );
};

export default ViewAdjusments;