import { getMicroserviceEndpoint, InternalV1EndpointOptions } from "@api-routes/endpoints";
import { fetcher } from "@utils/fetcher";
import { IFetchOptions } from "itsf-ui-common";
import { ICaseDTO, IUpdateCaseDTO, ICreateNoteDTO, INoteDTO } from "@modules/caseManagement/interfaces";

// === Endpoint base del microservicio ===
// Ajusta el nombre/segmento si tu gateway usa otro alias
const CASE_MGMT_ENDPOINT = "case-management-service/";
const CASE_MGMT_PRIVATE_ENDPOINT = getMicroserviceEndpoint(CASE_MGMT_ENDPOINT, {
    ...InternalV1EndpointOptions,
    useApiGateway: true,
});

const CASE_MGMT_PRIVATE_ENDPOINT_TEST = "http://localhost:13419/";



// GET /v1/caseManagement/cases/{id} - Get a case by id
export const getCaseById = (
  id: number | string,
  options: IFetchOptions = {}
) => {
  //const url = new URL(`${CASE_MGMT_PRIVATE_ENDPOINT}v1/caseManagement/cases/${id}`);
  const url = new URL(`${CASE_MGMT_PRIVATE_ENDPOINT}caseManagement/cases/${id}`);
  return fetcher<ICaseDTO>(url.toString(), {
    method: "GET",
    ...options,
  });
};

// PATCH /v1/caseManagement/cases/{id} - Update a case
export const updateCase = (
  id: number | string,
  payload: IUpdateCaseDTO,
  options: IFetchOptions = {}
) => {
  //const url = new URL(`${CASE_MGMT_PRIVATE_ENDPOINT}v1/caseManagement/cases/${id}`);
  const url = new URL(`${CASE_MGMT_PRIVATE_ENDPOINT}caseManagement/cases/${id}`);
  return fetcher<ICaseDTO>(url.toString(), {
    method: "PATCH",
    body: payload,
    ...options,
  });
};

// POST /v1/caseManagement/cases/{caseId}/notes - Create a note
export const createCaseNote = (
  caseId: number | string,
  payload: ICreateNoteDTO,
  options: IFetchOptions = {}
) => {
  //const url = new URL(`${CASE_MGMT_PRIVATE_ENDPOINT}v1/caseManagement/cases/${caseId}/notes`);
  const url = new URL(`${CASE_MGMT_PRIVATE_ENDPOINT}caseManagement/cases/${caseId}/notes`);
  return fetcher<INoteDTO>(url.toString(), {
    method: "POST",
    body: payload,
    ...options,
  });
};