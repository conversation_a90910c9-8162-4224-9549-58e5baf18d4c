export interface IUpdateCaseDTO {
  id?: number;               // int64
  globalCaseId?: number;     // int64
  assignedUserEmail?: string;
  requestedBy?: string;
  caseSubtypeId?: number;    // int64
  priorityId?: number;       // int64
  queueId?: number;          // int64
  technicalVisitId?: string;
  adjustmentId?: string;
  adjustmentType?: string;
  justification?: string;
  global?: boolean;
}